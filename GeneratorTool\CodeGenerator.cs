using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GeneratorTool
{
	public static class CodeGenerator
	{
		public static void Generate(string directoryPath)
		{
			var templates = Directory.GetFiles(Path.Combine(directoryPath, "VGiga", "Generators"), "*.verse").Select(
				x =>
				{
					var textLines = new List<string>();
					var textListNoUsings = new List<string>();
					bool checkForWhitespaceAfterUsings = false;
					foreach (var line in File.ReadAllLines(x))
					{
						if (line.Contains("<#") || line.Contains("#>"))
							continue;
						textLines.Add(line);
						if (!line.StartsWith("using"))
						{
							if (checkForWhitespaceAfterUsings)
							{
								if (!string.IsNullOrWhiteSpace(line))
								{
									checkForWhitespaceAfterUsings = false;
									textListNoUsings.Add(line);
								}
							}
							else
							{
								textListNoUsings.Add(line);
							}
						}
						else
						{
							checkForWhitespaceAfterUsings = true;
						}
					}
					var text = string.Join(Environment.NewLine, textLines);
					var textNoUsings = string.Join(Environment.NewLine, textListNoUsings);

					return new Template
					{
						Name = Path.GetFileNameWithoutExtension(x.Replace(".verse", "")),
						Text = text,
						LinesCount = textLines.Count,
						TextNoUsings = textNoUsings,
						LinesCountNoUsings = textListNoUsings.Count
					};
				}).ToDictionary(x => x.Name);
			templates.Add("con", new Template()
			{
				Name = "con"
			});
			foreach (var fileToGeneratePath in Directory.GetFiles(directoryPath, "*.verse", SearchOption.AllDirectories))
			{
				if (DirName(fileToGeneratePath) == "Generators" && DirName(Path.GetDirectoryName(fileToGeneratePath)) == "VGiga")
					continue;
				int id = 0;
				// string templateName = null;
				// List<string> vars = new();


				var useTabs = false;

				var lines = File.ReadAllLines(fileToGeneratePath);
				// First check if the file uses tabs
				if (lines.Any(l => l.Contains('\t')))
				{
					useTabs = true;
				}
				// Normalize indentation - convert spaces to tabs for consistency
				for (int i = 0; i < lines.Length; i++)
				{
					lines[i] = lines[i].Replace("    ", "\t");
				}

				var writeActionList = new List<WriteAction>();

				bool generateCode = false;
				id = 0;
				var firstGenId = 0;
				foreach (string line in lines)
				{
					if (line.Contains("#gen"))
					{
						generateCode = true;
						firstGenId = id;
						break;
					}
					id++;
				}

				if (!generateCode)
					continue;



				List<string> varsList = new();

				VerseType typeConstructor = new();
				List<VerseVariable> varsConstructor = new();

				for (int i = firstGenId; i < lines.Length; i++)
				{
					var line = lines[i];
					if (line.Contains("#gen"))
					{
						bool writeUsings = i == 0;
						varsList.Clear();
						varsConstructor.Clear();
						int genStartLine = i;
						i++;
						var nextLine = lines[i];


						var firstIndex = nextLine.IndexOf('#');
						var isDoubleCommented = firstIndex != nextLine.LastIndexOf('#') && firstIndex != -1;

						if (isDoubleCommented)
							continue;

						var templateName = nextLine.Trim().Replace("#", "");
						bool isConstructor = templateName == "con";
						var template = templates[templateName];

						string idLineText = null;

						// vars, start id line
						try
						{
							i++;
							while (i < lines.Length)
							{
								line = lines[i];
								if (!line.Contains("#"))
								{
									break;
								}

								if (line.Contains("id-"))
								{
									idLineText = line;
									i++;
									break;
								}
								line = line.Trim().Replace("#", "");
								varsList.Add(line);
								i++;
							}
						}
						catch (Exception exception)
						{
							Console.WriteLine(exception.ToString());
						}


						bool isWrittenAlready = false;
						int writeEndLine = 0;
						int topLengthToDelete = 2 + varsList.Count; //gen //template name

						if (idLineText != null)
						{
							for (int j = i; j < lines.Length; j++)
							{
								if (lines[j] == idLineText)
								{
									isWrittenAlready = true;
									writeEndLine = j;
									break;
								}
							}

							topLengthToDelete += 1;
						}
						else
						{
							idLineText = $"#id-{Guid.NewGuid()}";
						}

						if (isConstructor)
						{
							//constructor params

							var readStart = i;
							if (isWrittenAlready)
								readStart = writeEndLine + 1;
							var targetIndent = 1;
							var c = 0;
							while (lines[readStart][c] == '\t')
							{
								targetIndent++;
								c++;
							}
							typeConstructor = VerseType.FromLine(lines[readStart]);
							readStart++;
							FillArray(readStart, targetIndent);

							void FillArray(int lineNum, int targetIndent)
							{
								if (lineNum >= lines.Length)
									return;
								var curLine = lines[lineNum];
								if (string.IsNullOrWhiteSpace(curLine))
									return;
								var mVar = VerseVariable.FromLine(curLine, targetIndent);
								if (mVar is VerseVariable Var)
									varsConstructor.Add(Var);
								FillArray(lineNum + 1, targetIndent);
							}
						}

						var writeAction = new WriteAction();
						writeActionList.Add(writeAction);

						if (isWrittenAlready)
						{
							for (int j = genStartLine; j < writeEndLine + 1; j++)
								writeAction.DeleteList.Add(j);
						}
						else
						{
							for (int j = genStartLine; j < genStartLine + topLengthToDelete; j++)
								writeAction.DeleteList.Add(j);
						}

						string textToInsert = writeUsings ? template.Text : template.TextNoUsings;

						if (isConstructor)
						{
							var templateConstructor = Scriban.Template.Parse("""""
{{ classtype }}_c({{ for variable in variables }}P{{ variable.name }}:{{ variable.type }}{{if !for.last; ",\n	"; end}}{{ end }}
)<transacts>:{{ classtype }}=
	{{ classtype }}:
{{ for variable in variables }}		{{ variable.name }} := P{{ variable.name }}{{if !for.last; "\n"; end}}{{ end }}
""""");
							textToInsert = templateConstructor.Render(new { Variables = varsConstructor.Where(v => !v.IsAssigned), Classtype = typeConstructor.Name });
						}
						// int totalLength = (writeUsings ? template.LinesCount : template.LinesCountNoUsings) + 
						//                   // 1 + //gen
						//                   1 + //template name
						//                   1 + //length
						//                   1 + //end
						//                   varsList.Count;
						var NL = Environment.NewLine;
						textToInsert = $"{idLineText}{NL}{textToInsert}{NL}{idLineText}";


						id = 1;
						foreach (var varr in varsList)
						{
							string varInTemplate = $"VAR{id}";
							textToInsert = textToInsert.Replace(varInTemplate, varr);
							id++;
						}
						// outputTextBox.Text = textGen;
						varsList.Reverse();
						foreach (string varr in varsList)
						{
							textToInsert = $"#{varr}{NL}{textToInsert}";
						}
						textToInsert = $"#gen{NL}#{templateName}{NL}{textToInsert}";

						writeAction.InsertList.Add((genStartLine, textToInsert));
					}
				}

				writeActionList.Reverse();
				var linesList = lines.ToList();

				foreach (var action in writeActionList)
				{
					action.DeleteList.Reverse();
					action.InsertList.Reverse();
					foreach (var idDelete in action.DeleteList)
					{
						linesList.RemoveAt(idDelete);
					}
					foreach (var idAdd in action.InsertList)
					{
						linesList.Insert(idAdd.id, idAdd.text);
					}
				}
				string fulltext = string.Join(Environment.NewLine, linesList);
				File.WriteAllText(fileToGeneratePath, fulltext);
				// outputTextBox.Text = string.Join(Environment.NewLine, linesList);



				// using (StreamReader read = new StreamReader(fileToGeneratePath)) {
				// 	string line;
				// 	id = -1;
				// 	while ((line = read.ReadLine()) != null)
				// 	{
				// 		var lineNoComment = line.Replace("#", "");
				// 		if(id == -1)
				// 		{
				// 			templateName = lineNoComment;
				// 		}
				// 		else
				// 		{
				// 			vars.Add(lineNoComment);
				// 		}
				// 		id++;
				// 	}
				// }
				//
				// // var templateNameWithVars = Path.GetFileNameWithoutExtension(fileToGeneratePath).Replace(".verse", "");
				// // templateNameWithVars = templateNameWithVars.Substring(2, templateNameWithVars.Length - 2);
				// // var templateNameWithVars = fileToGeneratePath.Replace("\\g_", "\\");
				//
				// // List<string> vars = new();
				// // while(templateNameWithVars.LastIndexOf("__") != -1)
				// // {
				// // 	var idx = templateNameWithVars.LastIndexOf("__");
				// // 	
				// // 	vars.Add(templateNameWithVars.Substring(idx + 2, templateNameWithVars.Length - idx - 2));
				// // 	templateNameWithVars = templateNameWithVars.Substring(0, idx);
				// // }
				// // vars.Reverse();
				// // var templateName = templateNameWithVars;
				//
				// var template = templates[templateName];
				//
				// string textGen = template.Text.Replace("<#","").Replace("#>","");
				//
				// id = 1;
				// foreach(var varr in vars)
				// {
				// 	string varInTemplate = $"VAR{id}";
				// 	textGen = textGen.Replace(varInTemplate, varr);
				// 	id++;
				// }
				// // outputTextBox.Text = textGen;
				// vars.Reverse();
				// foreach(string varr in vars)
				// {
				// 	textGen = $"#{varr}{Environment.NewLine}{textGen}";
				// }
				// textGen = $"#{templateName}{Environment.NewLine}{textGen}";
				//
				// File.WriteAllText(fileToGeneratePath,textGen);
			}
			// outputTextBox.Text = "OKKKK";
		}
		static string DirName(string path)
		{
			return Path.GetFileName(Path.GetDirectoryName(path));
		}
	}
}
