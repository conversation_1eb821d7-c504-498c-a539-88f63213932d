﻿<Window x:Class="GeneratorTool.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GeneratorTool"
        xmlns:tb="http://www.hardcodet.net/taskbar"
        mc:Ignorable="d"
        Title="VerseCodeGen" Height="900" Width="800"
        Style="{StaticResource CustomWindowStyle}"
        >
	<!-- <Grid  Margin="10"> -->
	<StackPanel HorizontalAlignment="Stretch">
		<!-- <tb:TaskbarIcon x:Name="myNotifyIcon" -->
		<!--                 Visibility="Visible" -->
		<!--                 ToolTipText="Fallback ToolTip for Windows xp" -->
		<!--                 IconSource="/Logo.ico" -->
		<!--                 MenuActivation="LeftOrRightClick" -->
		<!--                 PopupActivation="DoubleClick" -->
		<!-- /> -->
		<TextBlock>Directory</TextBlock>
		<TextBox Name="inputDirectory"></TextBox>
		<Button Click="ButtonGenerate_OnClick">Generate Code!!</Button>

		<TextBlock>Prefabs Content Directory:</TextBlock>
		<TextBox Name="inputPrefabsDirectory"></TextBox>

		<TextBlock>Target Project Content Directory:</TextBlock>
		<TextBox Name="inputTargetDirectory"></TextBox>

		<TabControl>
			<TabItem Header="Copy all prefabs">
				<StackPanel HorizontalAlignment="Stretch">
					<TextBlock>Ignored Ids List:</TextBlock>
					<TextBox Name="inputIgnoreList" AcceptsReturn="True" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="200"></TextBox>
					<TextBlock>Don't override paths:</TextBlock>
					<TextBox Name="inputDontOverrideList" AcceptsReturn="True" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="200"></TextBox>
					<Button Click="ButtonCopyPrefabs_OnClick">Copy all Prefabs</Button>
					<Button Click="ButtonCopyPrefabsVerse_OnClick">SYNC Verse Only</Button>
				</StackPanel>
			</TabItem>
			<TabItem Header="Copy selected prefabs">
				<StackPanel HorizontalAlignment="Stretch">
					<TextBlock>Selected Ids List:</TextBlock>
					<TextBox Name="selectedIdList" AcceptsReturn="True" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="200"></TextBox>
					<Button Click="ButtonCopyPrefabsSelected_OnClick">Copy Prefabs</Button>
				</StackPanel>
			</TabItem>
			<TabItem Header="UMG To Verse">
				<StackPanel HorizontalAlignment="Stretch">
					<TextBlock>UMG:</TextBlock>
					<TextBox Name="umgText" AcceptsReturn="True" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="30"></TextBox>
					<TextBlock>Special cases:<LineBreak/>
                            - Skip in name to skip<LineBreak/>
                            - Text justify is missing (in UE editor)<LineBreak/>
                            - texture_blocks can't use transparency<LineBreak/>
                            - Append ColorBlock in Image name to make it color_block and use transparency
					</TextBlock>
					<Button Click="ButtonUMGToVerse_OnClick">Convert To Verse</Button>
				</StackPanel>
			</TabItem>
			<TabItem Header="Red V Blue">
				<StackPanel HorizontalAlignment="Stretch">
					<TextBlock>UMG:</TextBlock>
					<TextBox Name="redVBlueText" AcceptsReturn="True" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="30"></TextBox>
					<TextBlock>Special cases:<LineBreak/>
                            - Skip in name to skip<LineBreak/>
                            - Text justify is missing (in UE editor)<LineBreak/>
                            - texture_blocks can't use transparency<LineBreak/>
                            - Append ColorBlock in Image name to make it color_block and use transparency
					</TextBlock>
					<Button Click="ButtonRedVBlueToVerse_OnClick">Convert To Verse</Button>
				</StackPanel>
			</TabItem>
			<TabItem Header="ResizeTextures">
				<StackPanel HorizontalAlignment="Stretch">
					<TextBlock>TexturesFolder:</TextBlock>
					<TextBox Name="resizeTexturesFolderText" AcceptsReturn="False" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="30"></TextBox>
					<TextBlock>New size (square width:</TextBlock>
					<TextBox Name="resizeTexturesSizeText" AcceptsReturn="False" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="30"></TextBox>
					<Button Click="ResizeTextures_OnClick">Resize</Button>
				</StackPanel>
			</TabItem>
			<TabItem Header="From project to project">
				<StackPanel HorizontalAlignment="Stretch">
					<TextBlock>Original Project Name:</TextBlock>
					<TextBox Name="originalProjName" AcceptsReturn="False" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="30"></TextBox>
					<TextBlock>New Project Name:</TextBlock>
					<TextBox Name="newProjName" AcceptsReturn="False" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="30"></TextBox>
					<TextBlock>Data from clipboard!</TextBlock>
					<Button Click="ConvertProjToProj_OnClick">Convert</Button>
				</StackPanel>
			</TabItem>
		</TabControl>
		<TextBox Name="outputTextBox" AcceptsReturn="True" TextWrapping="Wrap"  VerticalScrollBarVisibility="Auto" MaxHeight="600" IsReadOnly="True"/>

	</StackPanel>
	<!-- </Grid> -->
</Window>
