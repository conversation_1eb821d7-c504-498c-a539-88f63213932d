﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
public interface IUnreal
{
	public string Name { get; set; }
}
public class UnrealVar : IUnreal
{
	public IUnreal Parent;
	public string Name { get; set; } = "";
	public string Value = "";
	public List<UnrealVar> Array = new();

	public VarType VarType;

	public UnrealVar GetVar(string name, string defaultString = "")
	{
		foreach (var varr in Array)
		{
			if (varr.Name == name)
			{
				return varr;
			}
		}
		return new UnrealVar(defaultString, this);
	}

	// 	public string ToVerseColor(int indent, string RGBName, string AlphaName)
	// 	{
	// 		var ret = $"""
	// {Idnt(indent)}{RGBName} := {RevalueVarVerse()}{'\n'} 
	//
	// """ ;
	// 	}

	public VStrings ToVerse(int indent, string? ColorName = null, string? AlphaName = null)
	{
		var v = new VStrings();
		if (VarType == VarType.array && Name != "Text")
		{
			string type = Name switch
			{
				"Alignment" => "vector2",
				"Offsets" => "margin",
				"Anchors" => "anchors",
				"Minimum" => "vector2",
				"Maximum" => "vector2",
				"ImageSize" => "vector2",
				"Padding" => "margin",
				"SpecifiedColor" => "color",
				"ShadowColorAndOpacity" => "color",
				"TextOverflowPolicy" => "DefaultOverflowPolicy",
				"ShadowOffset" => "option. vector2",
				_ => "missingType"
			};
			var ret = $"""
{Idnt(indent)}{RenameVarVerse(ColorName)} := {type}:

""";
			UnrealVar varA = null;
			foreach (var varr in Array)
			{
				if (varr.Name != "A")
					ret += varr.ToVerse(indent + 1);
				else
					varA = varr;
			}
			if (varA != null)// && float.Parse(varA.Value) != 1.0)
			{
				if (Name == "ShadowColorAndOpacity")
				{
					v.AddFuncString($"""
{((UnrealObj)Parent).Name.Trim()}.SetShadowOpacity({varA.Value})
""");
				}
				else if (AlphaName != null)
				{
					ret += $"""
{Idnt(indent)}{AlphaName} := {varA.Value}

""";
				}

			}
			v.MidString = ret;

			return v;
		}
		else
		{
			// this is for DefaultText only
			if (string.IsNullOrEmpty(Name))
				return v;
			else
			{
				v.MidString = $"""
{Idnt(indent)}{RenameVarVerse(ColorName)} := {RevalueVarVerse()}{'\n'}
""";
				if (Name == "Text")
				{


					//Method<localizes>:message = "Text"
					v.AddTopString($"{TextVarName()}<localizes>:message = {TextFromArray()}");
				}
			}
			return v;
		}
	}

	string RenameVarVerse(string ColorName = "missingColorName")
	{
		return Name switch
		{
			"bAutoSize" => "SizeToContent",
			"Content" => "Widget",
			"ResourceObject" => "DefaultImage",
			"ImageSize" => "DefaultDesiredSize",
			"SpecifiedColor" => ColorName,
			"Text" => "DefaultText",
			"ShadowColorAndOpacity" => "DefaultShadowColor",
			"ShadowOffset" => "DefaultShadowOffset",
			"Justification" => "DefaultJustification",
			_ => Name
		};
	}

	string RevalueVarVerse()
	{
		var ret = Name switch
		{
			"Content" => ClassNameToVerseName(Value),
			"ResourceObject" => ClassNameToVerseTexture(Value),
			"Text" => TextVarName(),
			"TextOverflowPolicy" => TextOverflowPolicy(),
			"Justification" => TextJustification(),
			"Orientation" => OrientationValue(),
			_ => Value
		};
		if (ret == Value)
		{
			ret = Value switch
			{
				"True" => "true",
				"False" => "false",
				"HAlign_Center" => "horizontal_alignment.Center",
				"VAlign_Center" => "vertical_alignment.Center",
				"HAlign_Fill" => "horizontal_alignment.Fill",
				"VAlign_Fill" => "vertical_alignment.Fill",
				"HAlign_Left" => "horizontal_alignment.Left",
				"HAlign_Right" => "horizontal_alignment.Right",
				"VAlign_Top" => "vertical_alignment.Top",
				"VAlign_Bottom" => "vertical_alignment.Bottom",
				_ => Value
			};
		}
		return ret;
	}
	string OrientationValue()
	{
		if (Value == "Orient_Vertical")
			return "orientation.Vertical";
		return "orientation.Horizontal";
	}
	string TextOverflowPolicy()
	{
		if (Value.Contains("Ellipsis"))
		{
			return "text_overflow_policy.Ellipsis";
		}
		return "text_overflow_policy.Clip";
	}
	string TextJustification()
	{
		if (Value.Contains("Center"))
		{
			return "text_justification.Center";
		}
		if (Value.Contains("Left"))
		{
			return "text_justification.InvariantLeft";
		}
		if (Value.Contains("Right"))
		{
			return "text_justification.InvariantRight";
		}
		return "text_justification.InvariantLeft";
	}

	string TextFromArray()
	{
		return Array[Array.Count - 1].Name.Replace(" ", "");
	}
	string TextVarName()
	{
		return Parent.Name + "TextVar";
	}

	public static string ClassNameToVerseName(string str)
	{
		return str.Split("'")[1];
	}
	string ClassNameToVerseTexture(string str)
	{
		var input = ClassNameToVerseName(str);
		string pattern = @"^/[^/]*/(.*)$";

		Match match = Regex.Match(input, pattern);
		if (match.Success)
		{
			Console.WriteLine("Extracted part: " + match.Groups[1].Value);
		}

		return match.Groups[1].Value // TTextures/UI/T_Panel.T_Panel
			.Split('.')[0]         // TTextures/UI/T_Panel
			.Replace("/", ".");
	}

	public static string Idnt(int indent)
	{
		string indentStr = "";
		for (int i = 0; i < indent; i++)
		{
			indentStr += "\t";
		}
		return indentStr;
	}


	public UnrealVar(string str, IUnreal parent)
	{
		Parent = parent;
		int eqId = str.IndexOf("=");
		if (eqId == -1)
		{
			Name = str;
			return;
		}
		Name = str.Substring(0, eqId);
		Value = str.Substring(eqId + 1);

		Value = Value.Replace("NSLOCTEXT", "");
		Value = Value.Replace("INVTEXT", "");
		if (Value.StartsWith('"'))
			Value = Value.Substring(1, Value.LastIndexOf('"') - 1);

		if (Value.StartsWith('('))
		{
			VarType = VarType.array;
			var arrayStr = Value.Substring(1, Value.LastIndexOf(')') - 1);
			List<string> varsStr = new();
			int openedBrackets = 0;
			int startId = 0;
			for (int i = 0; i < arrayStr.Length; i++)
			{
				char c = arrayStr[i];
				if (c == '(')
					openedBrackets++;
				if (c == ')')
					openedBrackets--;
				if (openedBrackets == 0 && (c == ','))
				{
					varsStr.Add(arrayStr.Substring(startId, i - startId));
					startId = i + 1;
				}
				if (openedBrackets == 0 && i == arrayStr.Length - 1)
				{
					varsStr.Add(arrayStr.Substring(startId, i - startId + 1));
					startId = i + 1;
				}
			}

			Array = varsStr.Select(m => new UnrealVar(m, this)).ToList();
		}
		else if (float.TryParse(Value[0].ToString(), out float _))
			VarType = VarType.floatt;
		else
			VarType = VarType.stringg;
	}
}
