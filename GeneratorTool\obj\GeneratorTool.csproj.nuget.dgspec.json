{"format": 1, "restore": {"H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\GeneratorTool.csproj": {}}, "projects": {"H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\GeneratorTool.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\GeneratorTool.csproj", "projectName": "GeneratorTool", "projectPath": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\GeneratorTool.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["G:\\ProgramsDev\\MicrosoftVisualStudioShared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Hardcodet.NotifyIcon.Wpf": {"target": "Package", "version": "[1.1.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Scriban": {"target": "Package", "version": "[5.9.0, )"}, "WindowsInput": {"target": "Package", "version": "[6.4.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}