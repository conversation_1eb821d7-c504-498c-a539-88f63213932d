{"Version": 1, "WorkspaceRootPath": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\unrealvar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\unrealvar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\vstrings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\vstrings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\umgtoverse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\umgtoverse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\parser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\parser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\codegenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\codegenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortooltests\\res\\red v blue big test.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|solutionrelative:generatortooltests\\res\\red v blue big test.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\redvblue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\redvblue.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\hotkeyhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\hotkeyhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\imageutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\imageutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\generatortool.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\generatortool.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\windowsclipboard.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\windowsclipboard.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortooltests\\res\\testdataredvblue withparent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|solutionrelative:generatortooltests\\res\\testdataredvblue withparent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortooltests\\testdataumgtoverse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|solutionrelative:generatortooltests\\testdataumgtoverse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortooltests\\unittest1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|solutionrelative:generatortooltests\\unittest1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortooltests\\globalusings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B787FFFE-5A7A-4CC6-BD63-2F6CA2DDAB3F}|GeneratorToolTests\\GeneratorToolTests.csproj|solutionrelative:generatortooltests\\globalusings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|h:\\unrealfnprojects\\a_csharptools\\generatortool\\generatortool\\unrealobj.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F0968508-2C5E-4901-8408-5287DA914CE3}|GeneratorTool\\GeneratorTool.csproj|solutionrelative:generatortool\\unrealobj.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 361, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\MainWindow.xaml", "RelativeDocumentMoniker": "GeneratorTool\\MainWindow.xaml", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\MainWindow.xaml", "RelativeToolTip": "GeneratorTool\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2024-05-28T00:26:11.414Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "UMGToVerse.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\UMGToVerse.cs", "RelativeDocumentMoniker": "GeneratorTool\\UMGToVerse.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\UMGToVerse.cs", "RelativeToolTip": "GeneratorTool\\UMGToVerse.cs", "ViewState": "AgIAAEIAAAAAAAAAAAAmwHEAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T00:26:11.413Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "UnrealVar.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\UnrealVar.cs", "RelativeDocumentMoniker": "GeneratorTool\\UnrealVar.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\UnrealVar.cs", "RelativeToolTip": "GeneratorTool\\UnrealVar.cs", "ViewState": "AgIAAJ4AAAAAAAAAAIA0wL8AAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T00:26:11.412Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "ImageUtils.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\ImageUtils.cs", "RelativeDocumentMoniker": "GeneratorTool\\ImageUtils.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\ImageUtils.cs", "RelativeToolTip": "GeneratorTool\\ImageUtils.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-06-04T16:13:00.383Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "CodeGenerator.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\CodeGenerator.cs", "RelativeDocumentMoniker": "GeneratorTool\\CodeGenerator.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\CodeGenerator.cs", "RelativeToolTip": "GeneratorTool\\CodeGenerator.cs", "ViewState": "AgIAADIAAAAAAAAAAAAAAAQAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T23:12:13.655Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Parser.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\Parser.cs", "RelativeDocumentMoniker": "GeneratorTool\\Parser.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\Parser.cs", "RelativeToolTip": "GeneratorTool\\Parser.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T00:26:11.296Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "App.xaml", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\App.xaml", "RelativeDocumentMoniker": "GeneratorTool\\App.xaml", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\App.xaml", "RelativeToolTip": "GeneratorTool\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-21T21:50:08.156Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "red v blue big test.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\Res\\red v blue big test.cs", "RelativeDocumentMoniker": "GeneratorToolTests\\Res\\red v blue big test.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\Res\\red v blue big test.cs", "RelativeToolTip": "GeneratorToolTests\\Res\\red v blue big test.cs", "ViewState": "AgIAAHIVAAAAAAAAAAAkwHwVAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T02:01:21.526Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "HotKeyHelper.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\HotKeyHelper.cs", "RelativeDocumentMoniker": "GeneratorTool\\HotKeyHelper.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\HotKeyHelper.cs", "RelativeToolTip": "GeneratorTool\\HotKeyHelper.cs", "ViewState": "AgIAADsAAAAAAAAAAAA0wG0AAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-12T22:09:47.723Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "VStrings.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\VStrings.cs", "RelativeDocumentMoniker": "GeneratorTool\\VStrings.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\VStrings.cs", "RelativeToolTip": "GeneratorTool\\VStrings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T00:26:11.411Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "GeneratorTool.csproj", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\GeneratorTool.csproj", "RelativeDocumentMoniker": "GeneratorTool\\GeneratorTool.csproj", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\GeneratorTool.csproj", "RelativeToolTip": "GeneratorTool\\GeneratorTool.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-12-03T20:09:23.475Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "WindowsClipboard.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\WindowsClipboard.cs", "RelativeDocumentMoniker": "GeneratorTool\\WindowsClipboard.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\WindowsClipboard.cs", "RelativeToolTip": "GeneratorTool\\WindowsClipboard.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T01:31:33.552Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "TestDataRedVBlue WithParent.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\Res\\TestDataRedVBlue WithParent.cs", "RelativeDocumentMoniker": "GeneratorToolTests\\Res\\TestDataRedVBlue WithParent.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\Res\\TestDataRedVBlue WithParent.cs", "RelativeToolTip": "GeneratorToolTests\\Res\\TestDataRedVBlue WithParent.cs", "ViewState": "AgIAABUAAAAAAAAAAAAnwCEAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T01:19:35.147Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "TestDataUMGToVerse.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\TestDataUMGToVerse.cs", "RelativeDocumentMoniker": "GeneratorToolTests\\TestDataUMGToVerse.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\TestDataUMGToVerse.cs", "RelativeToolTip": "GeneratorToolTests\\TestDataUMGToVerse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T21:32:09.856Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "UnitTest1.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\UnitTest1.cs", "RelativeDocumentMoniker": "GeneratorToolTests\\UnitTest1.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\UnitTest1.cs", "RelativeToolTip": "GeneratorToolTests\\UnitTest1.cs", "ViewState": "AgIAAAsAAAAAAAAAAAArwCYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T00:26:11.418Z"}]}, {"DockedWidth": 773, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "GeneratorTool\\MainWindow.xaml.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\MainWindow.xaml.cs", "RelativeToolTip": "GeneratorTool\\MainWindow.xaml.cs", "ViewState": "AgIAAGcBAAAAAAAAAAAcwIoBAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T16:01:49.051Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "GlobalUsings.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\GlobalUsings.cs", "RelativeDocumentMoniker": "GeneratorToolTests\\GlobalUsings.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorToolTests\\GlobalUsings.cs", "RelativeToolTip": "GeneratorToolTests\\GlobalUsings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T01:33:02.84Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "RedVBlue.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\RedVBlue.cs", "RelativeDocumentMoniker": "GeneratorTool\\RedVBlue.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\RedVBlue.cs", "RelativeToolTip": "GeneratorTool\\RedVBlue.cs", "ViewState": "AgIAAIMAAAAAAAAAAAAcwKgAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-29T15:57:38.004Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "UnrealObj.cs", "DocumentMoniker": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\UnrealObj.cs", "RelativeDocumentMoniker": "GeneratorTool\\UnrealObj.cs", "ToolTip": "H:\\UnrealFnProjects\\A_CsharpTools\\GeneratorTool\\GeneratorTool\\UnrealObj.cs", "RelativeToolTip": "GeneratorTool\\UnrealObj.cs", "ViewState": "AgIAAEQAAAAAAAAAAAAkwGUAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-05-28T00:26:11.413Z"}]}]}]}