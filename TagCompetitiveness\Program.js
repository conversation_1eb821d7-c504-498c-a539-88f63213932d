const url = "https://fortnite.gg/player-count";
const response = await fetch(url);
const content = await response.text();
console.log(content);

const matches = content.matchAll(/<option value='(\d+)'>([^<]+)<\/option>/g);
const matchesArray = Array.from(matches);

const tags = matchesArray
    .filter(m => m[1] !== "0") // Skip the "Filter by tag" option
    .map(m => ({
        id: parseInt(m[1]),
        name: m[2]
    }));

tags.forEach(tag => {
    console.log(`${tag.id}: ${tag.name}`);
});

const devMode = false; // Set to true for testing with only first 5 tags
const tagsToProcess = devMode ? tags.slice(0, 1) : tags;

const tagResponses = [];

for (const tag of tagsToProcess) {
	const tagUrl = `https://fortnite.gg/player-count-releases?tag=${tag.id}`;
	const tagResponse = await fetch(tagUrl);
	const tagContent = await tagResponse.text();
	console.log(`Content for tag ${tag.id}: ${tagContent}`);

	// Example response: {"start":1640995200,"step":86400,"values":[0,0,1,0,0,0,0,0,1,0,0,0,0,0,0,1,0,0,0,0,0,0,1,3,0,0,0,0,0,1,0,0,0,0,0,2,2,0,0,1,1,1,0,1,0,0,0,0,0,1,0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,1,0,1,0,0,1,0,2,0,2,0,0,0,1,0,0,2,1,1,0,0,1,0,2,1,1,1,1,0,1,0,0,1,2,0,0,0,1,1,1,0,0,2,2,0,0,0,2,0,1,0,0,0,1,3,2,1,0,0,2,2,2,0,0,0,0,0,1,0,1,1,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,1,0,0,0,0,0,1,0,1,1,0,2,1,0,0,0,1,0,0,0,0,0,1,1,0,1,0,0,0,0,1,0,0,0,1,0,1,0,0,0,1,0,0,1,2,0,0,0,1,0,1,0,0,0,1,0,0,1,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,1,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,2,0,0,0,1,0,1,0,2,1,1,0,0,0,2,2,0,0,0,0,0,1,0,0,1,0,0,0,0,0,0,1,0,0,0,0,0,1,1,0,0,0,2,0,0,2,0,0,1,0,1,0,2,0,1,0,0,4,1,0,0,0,0,1,0,0,0,0,1,0,1,1,0,0,0,0,0,0,1,1,0,0,0,0,2,1,0,1,2,1,2,1,0,2,1,1,0,2,1,0,1,1,1,0,0,2,0,1,0,1,2,0,1,1,1,0,2,0,0,1,0,1,1,0,1,2,1,0,0,0,1,0,0,1,1,0,1,0,1,0,0,1,1,0,0,0,2,0,0,0,2,1,0,1,0,1,0,0,1,2,0,1,0,1,1,2,1,0,1,2,1,5,0,1,5,2,2,4,0,1,3,1,2,1,5,3,1,2,0,2,1,2,5,4,3,3,1,2,1,3,2,5,1,3,3,3,2,2,6,5,2,2,2,5,9,1,1,0,4,2,1,6,0,2,1,1,6,0,2,2,9,2,5,1,2,2,2,5,1,1,3,2,5,2,3,3,2,4,4,1,6,2,3,3,1,2,2,5,2,5,5,0,2,2,2,3,3,2,5,2,5,4,5,5,2,4,4,6,1,3,2,1,2,1,2,10,4,0,1,2,3,3,3,10,4,4,1,4,6,4,4,0,9,1,8,9,4,6,5,3,10,9,6,3,4,7,4,8,3,4,1,5,4,6,4,6,3,3,5,1,2,5,2,1,5,7,8,7,4,2,5,2,2,4,4,3,7,4,2,3,5,3,5,2,1,3,3,5,3,4,4,1,2,3,2,3,4,2,3,9,4,1,3,2,3,3,2,1,2,0,3,1,1,5,1,3,5,2,2,2,1,3,3,3,0,4,0,2,3,4,3,5,1,5,1,7,4,2,2,1,5,2,9,3,1,4,4,3,5,3,4,4,5,1,3,4,4,4,1,6,6,3,1,4,6,2,2,5,5,4,4,6,1,3,5,5,4,6,2,7,2,1,3,6,6,6,6,5,5,5,8,16,8,8,9,7,6,8,14,9,5,4,5,4,7,7,7,4,6,7,5,9,5,7,2,4,5,9,7,10,5,6,7,11,16,6,11,12,14,4,11,6,5,8,6,3,5,2,3,13,10,6,11,4,5,6,8,8,7,5,2,4,7,6,6,0,5,4,3,4,3,5,3,8,7,6,2,4,7,13,6,2,5,6,2,9,3,9,6,4,5,7,3,6,3,4,3,9,6,7,6,2,3,1,3,4,9,9,2,5,4,5,4,3,5,5,1,1,4,5,2,5,7,5,4,6,2,5,3,1,5,2,4,2,1,3,2,3,4,3,3,3,2,2,3,8,5,6,2,4,6,4,9,2,1,2,4,5,4,4,1,5,3,7,3,3,3,1,1,1,5,5,4,4,1,3,5,4,4,3,2,2,3,2,4,5,2,3,5,2,2,1,2,3,5,3,1,1,2,4,4,1,1,3,2,2,1,3,5,0,5,9,1,6,0,2,2,4,6,5,5,1,4,2,4,8,5,2,3,7,1,4,3,3,4,3,4,3,2,4,4,1,3,3,0,4,4,0,0,1,6,3,2,2,1,4,2,3,3,3,2,1,4,4,2,4,0,4,1,2,4,1,4,3,4,3,0,3,3,4,2,3,4,1,2,1,2,0,3,0,3,0,6,2,3,5,2,1,3,0,3,0,5,3,4,4,4,7,4,3,2,8,5,7,1,5,3,3,1,1,5,4,4,3,1,5,3,3,4,2,5,5,1,5,2,4,3,9,4,4,3,5,2,4,1,2,6,4,5,16,7,5,8,1,4,1,4,6,8,1,3,4,3,7,12,6,9,5,6,5,10,7,9,5,2,5,2,2,3,4,5,3,4,4,2,18,22,4,2,4,8,6,12,7,3,9,3,4,6,5,4,4,5,8,8,7,5,5,9,6,4,1,7,7,5,4,8,4,7,6,3,10,7,3,6,4,8,6,4,6,4,6,4,3,2,3,2,1,7,9,3,6,8,7,5,7,5,4,5,4,5,4]}

	try {
		const tagData = JSON.parse(tagContent);
		// Get last 30 values or all values if less than 30
		const last30Values = tagData.values.slice(-30);
		const lastValueLast30Days = last30Values.reduce((sum, value) => sum + value, 0);
		
		tagResponses.push({
			tagId: tag.id,
			tagName: tag.name,
			start: tagData.start,
			step: tagData.step,
			lastValueLast30Days
		});
	} catch (error) {
		console.error(`Error parsing JSON for tag ${tag.id}: ${error.message}`);
	}
	
	// Add sleep to not spam the server
	await new Promise(resolve => setTimeout(resolve, 300)); // Sleep for 1 second
}

console.log('Tag responses:', tagResponses);

async function fetchTagsWithCount() {
	const url = "https://fortnite.gg/player-count?genres";
	const response = await fetch(url);
	const content = await response.text();
	const regex = /<div class='rank'>(\d+)<\/div>([^<]+)<\/div><div class='column-2'><div class='peak' style='color:#fff'>([^<]+) <span>Maps<\/span><\/div><div class='ccu'>([^<]+) <span>Players Now<\/span>/g;
	const matches = content.matchAll(regex);
	const tagsWithCount = Array.from(matches).map(match => ({
		rank: parseInt(match[1]),
		name: match[2].trim(),
		maps: parseInt(match[3].replace(',', '')),
		players: parseInt(match[4].replace(',', ''))
	}));

	return tagsWithCount;
}

const tagsWithCount = await fetchTagsWithCount();
console.log('Extracted tags:', tagsWithCount);

// Combine tagResponses and tagsWithCount
const combinedTags = tagResponses.map(tagResponse => {
	const matchingTagWithCount = tagsWithCount.find(t => t.name === tagResponse.tagName);
	return {
		...tagResponse,
		...matchingTagWithCount
	};
});

console.log('Combined tags:', combinedTags);

// Create CSV from combined tags
const csvHeader = "tagId,tagName,start,step,lastValueLast30Days,rank,maps,players\n";
const csvRows = combinedTags.map(tag => 
	`${tag.tagId},${tag.tagName},${tag.start},${tag.step},${tag.lastValueLast30Days},${tag.rank},${tag.maps},${tag.players}`
).join("\n");
const csv = csvHeader + csvRows;

console.log('CSV output:');
console.log(csv);

// Optionally, you can write the CSV to a file
// const fs = require('fs');
// fs.writeFileSync('combined_tags.csv', csv);

