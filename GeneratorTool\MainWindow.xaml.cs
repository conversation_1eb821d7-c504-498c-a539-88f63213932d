using HotKeyTools;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.TextFormatting;
using Expression = System.Linq.Expressions.Expression;

namespace GeneratorTool;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
	HotKeyHelper _hotKeys;
	int _throwConfettiKeyId;

	public MainWindow()
	{
		InitializeComponent();
	}


	List<(TextBox, string)> textBoxesToLoadAndSave;

	protected override void OnSourceInitialized(EventArgs e)
	{
		base.OnSourceInitialized(e);
		// add stuff here
		textBoxesToLoadAndSave = new()
		{
			(originalProjName, nameof(originalProjName)),
			(newProjName, nameof(newProjName)),
		};

		foreach (var item in textBoxesToLoadAndSave)
		{
			item.Item1.Text = LoadInput(item.Item2 + ".savetxt");
		}
		inputDirectory.Text = LoadInput("printini.txt");
		inputPrefabsDirectory.Text = LoadInput("saveprefabs.txt");
		inputTargetDirectory.Text = LoadInput("savetarget.txt");
		inputIgnoreList.Text = LoadInput("ignorelist.txt");
		inputDontOverrideList.Text = LoadInput("overridelist.txt");
		redVBlueText.Text = LoadInput("redvblue.txt");


		_hotKeys = new HotKeyHelper(this);

		// Assign Ctrl-Alt-C to our ThrowConfetti() method.
		_throwConfettiKeyId = _hotKeys.ListenForHotKey(
			Key.G,
			HotKeyModifiers.Shift | HotKeyModifiers.Control,
			() => { ButtonGenerate_OnClick(null, null); } // put any code you want here
		);
	}
	// private static int CountLines(string s)
	// {
	// 	int n = 0;
	// 	foreach( var c in s )
	// 	{
	// 		if ( c == '\n' ) n++;
	// 	}
	// 	return n+1;
	// }

	void ButtonCopyPrefabsSelected_OnClick(object sender, RoutedEventArgs e)
	{
		ButtonCopyPrefabsSelected_OnClickAsync(sender, e);
	}

	async void ButtonCopyPrefabsSelected_OnClickAsync(object sender, RoutedEventArgs e)
	{
		if (!BtnCopCheck())
			return;

		outputTextBox.Text = "Coping...";
		await Task.Delay(50);
		var selectedPaths = selectedIdList.Text.Split(
			new[] { "\r\n", "\r", "\n" },
			StringSplitOptions.None
		).Select(x => x.Replace("/", "\\")).ToList();

		var prefabsDir = inputPrefabsDirectory.Text.Replace("/", "\\");
		var targetDir = inputTargetDirectory.Text.Replace("/", "\\");
		foreach (var prefabFile in selectedPaths)
		{
			try
			{
				if (!prefabFile.StartsWith(prefabsDir))
					throw new Exception($"'{prefabFile}' not starting with: {prefabsDir}");
				var newFile = prefabFile.Replace(prefabsDir, targetDir);
				Directory.CreateDirectory(Path.GetDirectoryName(newFile));
				File.Copy(prefabFile, newFile, true);
			}
			catch (Exception exception)
			{
				outputTextBox.Text = exception.ToString();
				return;
			}
		}
		outputTextBox.Text = "Copy ok!";
	}

	bool BtnCopCheck()
	{
		if (Path.GetFileName(Path.GetDirectoryName(inputPrefabsDirectory.Text)) != "Content")
		{
			outputTextBox.Text = "Error: prefabs folder in not content!";
			return false;
		}
		if (Path.GetFileName(Path.GetDirectoryName(inputTargetDirectory.Text)) != "Content")
		{
			outputTextBox.Text = "Error: target folder in not content!";
			return false;
		}
		SaveInput();
		return true;
	}
	void ButtonCopyPrefabsVerse_OnClick(object sender, RoutedEventArgs e)
	{
		ButtonCopyPrefabsVerse_OnClick_OnClickAsync(sender, e);
	}
	async void ButtonCopyPrefabsVerse_OnClick_OnClickAsync(object sender, RoutedEventArgs e)
	{
		if (!BtnCopCheck())
			return;

		outputTextBox.Text = "Coping...";
		await Task.Delay(50);

		var ignoredIds = inputIgnoreList.Text.Split(
			new[] { "\r\n", "\r", "\n" },
			StringSplitOptions.None
		).ToHashSet();
		var dontOverridePaths = inputDontOverrideList.Text.Split(
			new[] { "\r\n", "\r", "\n" },
			StringSplitOptions.None
		).ToList();

		var prefabsDir = inputPrefabsDirectory.Text;
		var targetDir = inputTargetDirectory.Text;

		var prefabFilesPaths = Directory.GetFiles(inputPrefabsDirectory.Text, "*.verse", SearchOption.AllDirectories)
			.Where(x =>
			{
				if (ignoredIds.Contains(Path.GetFileNameWithoutExtension(x)))
					return false;
				for (var i = 0; i < dontOverridePaths.Count; i++)
				{
					string? dontOverridePath = dontOverridePaths[i];
					if (x.EndsWith(dontOverridePath))
					{
						var newFile = x.Replace(prefabsDir, targetDir);
						if (File.Exists(newFile))
						{
							dontOverridePaths.RemoveAt(i);
							return false;
						}
					}
				}
				return true;
			}).ToArray();

		// outputTextBox.Text = "Files to copy:\n"+ string.Join(Environment.NewLine, prefabFilesPaths);
		foreach (var prefabFile in prefabFilesPaths)
		{
			try
			{
				var newFile = prefabFile.Replace(prefabsDir, targetDir);

				Directory.CreateDirectory(Path.GetDirectoryName(newFile));
				FileInfo prefabInfo = new(prefabFile);
				FileInfo targetInfo = new(newFile);

				if (prefabInfo.LastWriteTime > targetInfo.LastWriteTime)
				{
					File.Copy(prefabFile, newFile, true);
				}
				else if (prefabInfo.LastWriteTime < targetInfo.LastWriteTime)
				{
					File.Copy(newFile, prefabFile, true);
				}
			}
			catch (Exception exception)
			{
				outputTextBox.Text = exception.ToString();
				return;
			}
		}
		outputTextBox.Text = "Copy ok!";
	}

	void ButtonCopyPrefabs_OnClick(object sender, RoutedEventArgs e)
	{
		ButtonCopyPrefabs_OnClickAsync(sender, e);
	}

	async void ButtonCopyPrefabs_OnClickAsync(object sender, RoutedEventArgs e)
	{
		if (!BtnCopCheck())
			return;

		outputTextBox.Text = "Coping...";

		await Task.Delay(50);

		var ignoredIds = inputIgnoreList.Text.Split(
			new[] { "\r\n", "\r", "\n" },
			StringSplitOptions.None
		).ToHashSet();
		var dontOverridePaths = inputDontOverrideList.Text.Split(
			new[] { "\r\n", "\r", "\n" },
			StringSplitOptions.None
		).ToList();

		var prefabsDir = inputPrefabsDirectory.Text;
		var targetDir = inputTargetDirectory.Text;

		var prefabFilesPaths = Directory.GetFiles(inputPrefabsDirectory.Text, "*.*", SearchOption.AllDirectories)
			.Where(x =>
			{
				if (ignoredIds.Contains(Path.GetFileNameWithoutExtension(x)))
					return false;
				for (var i = 0; i < dontOverridePaths.Count; i++)
				{
					string? dontOverridePath = dontOverridePaths[i];
					if (x.EndsWith(dontOverridePath))
					{
						var newFile = x.Replace(prefabsDir, targetDir);
						if (File.Exists(newFile))
						{
							dontOverridePaths.RemoveAt(i);
							return false;
						}
					}
				}
				return true;
			}).ToArray();

		// outputTextBox.Text = "Files to copy:\n"+ string.Join(Environment.NewLine, prefabFilesPaths);
		foreach (var prefabFile in prefabFilesPaths)
		{
			try
			{
				var newFile = prefabFile.Replace(prefabsDir, targetDir);
				Directory.CreateDirectory(Path.GetDirectoryName(newFile));
				File.Copy(prefabFile, newFile, true);
			}
			catch (Exception exception)
			{
				outputTextBox.Text = exception.ToString();
				return;
			}
		}
		outputTextBox.Text = "Copy ok!";
	}

	void ButtonGenerate_OnClick(object sender, RoutedEventArgs e)
	{
		try
		{
			if (inputDirectory.Text.Contains("\\"))
			{
				inputDirectory.Text = inputDirectory.Text.Replace("\\", "/");
			}
			var folder = Path.GetFileName(Path.GetDirectoryName(inputDirectory.Text));
			SaveInput();
			if (folder != "Content")
			{
				outputTextBox.Text = $"Error: folder in not content! Folder: {folder}";
				return;
			}
			CodeGenerator.Generate(inputDirectory.Text);
		}
		catch (Exception exception)
		{
			outputTextBox.Text = exception.ToString();
			// Debug.LogException(exception);
			// throw;
		}
	}


	string LoadInput(string filename)
	{
		try
		{
			return File.ReadAllText(GetInputPath(filename));
		}
		catch (Exception e)
		{
			return "";
		}
	}

	void SaveInput()
	{
		File.WriteAllText(GetInputPath("printini.txt"), inputDirectory.Text);
		File.WriteAllText(GetInputPath("saveprefabs.txt"), inputPrefabsDirectory.Text);
		File.WriteAllText(GetInputPath("savetarget.txt"), inputTargetDirectory.Text);
		File.WriteAllText(GetInputPath("ignorelist.txt"), inputIgnoreList.Text);
		File.WriteAllText(GetInputPath("overridelist.txt"), inputDontOverrideList.Text);
		File.WriteAllText(GetInputPath("redvblue.txt"), redVBlueText.Text);

		foreach (var item in textBoxesToLoadAndSave)
		{
			File.WriteAllText(GetInputPath(item.Item2 + ".savetxt"), item.Item1.Text);
		}
	}

	string GetInputPath(string filename)
	{
		string applicationPath = Path.GetFullPath(AppDomain.CurrentDomain.BaseDirectory); // the directory that your program is installed in
		return Path.Combine(applicationPath, filename);
	}
	void ButtonUMGToVerse_OnClick(object sender, RoutedEventArgs e)
	{
		try
		{
			umgText.Text = WindowsClipboard.GetText();
			outputTextBox.Text = UMGToVerse.Convert(umgText.Text);
			WindowsClipboard.SetText(outputTextBox.Text);
		}
		catch (Exception exception)
		{
			outputTextBox.Text = exception.ToString();
			// Debug.LogException(exception);
			// throw;
		}
	}
	void ButtonRedVBlueToVerse_OnClick(object sender, RoutedEventArgs e)
	{
		try
		{
			redVBlueText.Text = WindowsClipboard.GetText();
			outputTextBox.Text = new RedVBlue().ConvertBlueToRed(redVBlueText.Text);
			WindowsClipboard.SetText(outputTextBox.Text);
		}
		catch (Exception exception)
		{
			outputTextBox.Text = exception.ToString();
			// Debug.LogException(exception);
			// throw;
		}
		SaveInput();
	}

	void ResizeTextures_OnClick(object sender, RoutedEventArgs e)
	{
		try
		{
			outputTextBox.Text = "Resizing...\n";
			var folder = resizeTexturesFolderText.Text.Replace("\\", "/");
			var size = int.Parse(resizeTexturesSizeText.Text);
			var dir = new DirectoryInfo(folder);
			foreach (var file in dir.EnumerateFiles("*.png"))
			{
				outputTextBox.Text += $"Resizing: {file.FullName}\n";
				var bitmap = ImageUtils.ResizeAndCenter(file.FullName, size);
				var newPathFolder = Path.GetDirectoryName(file.FullName) + "\\" + "Resized";
				Directory.CreateDirectory(newPathFolder);
				var newPath = newPathFolder + "\\" + Path.GetFileNameWithoutExtension(file.FullName) + $"_{size}.png";
				outputTextBox.Text += $"New path: {newPath}\n";
				bitmap.Save(newPath, ImageFormat.Png);
			}
		}
		catch (Exception exception)
		{
			outputTextBox.Text = exception.ToString();
		}
	}

	void ConvertProjToProj_OnClick(object sender, RoutedEventArgs e)
	{
		try
		{
			outputTextBox.Text = "Converting...\n";
			var projName = originalProjName.Text;
			var projNameNew = newProjName.Text;
			var data = WindowsClipboard.GetText();
			var newData = data.Replace(projName, projNameNew);
			WindowsClipboard.SetText(newData);
			outputTextBox.Text += "Done! New data in clipboard.\n";
			SaveInput();
		}
		catch (Exception exception)
		{
			outputTextBox.Text = exception.ToString();
		}
	}

}
class Template
{
	public string Name;
	public string Text;
	public string TextNoUsings;
	public int LinesCount;
	public int LinesCountNoUsings;
}

struct VerseVariable
{
	public string Name;
	public string Type;
	public AccessType AccessType;
	public bool IsAssigned;

	public static VerseVariable? FromLine(string line, int targetIntend)
	{
		for (int i = 0; i < targetIntend; i++)
		{
			if (line[i] != '\t')
				return null;
		}
		if (line[targetIntend] == '\t')
			return null;
		var args = line.Split(':', StringSplitOptions.TrimEntries);
		args[0] = args[0].Replace("var", "").Trim();
		var nameWithAccess = args[0].Split(new[] { '<', '>' }, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

		var access = nameWithAccess.Length == 1 ? AccessType.Internal : Enum.Parse<AccessType>(char.ToUpper(nameWithAccess[1][0]) + nameWithAccess[1].Substring(1));
		var typeWithAssignment = args[1].Split(new[] { "=" }, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

		return new VerseVariable
		{
			Name = nameWithAccess[0],
			Type = typeWithAssignment[0],
			AccessType = access,
			IsAssigned = typeWithAssignment.Length > 1
		};
	}
}

enum AccessType
{
	Private,
	Public,
	Internal
}

struct VerseType
{
	public string Name = "";
	public VerseType()
	{
	}

	public static VerseType FromLine(string line)
	{
		var args = line.Split(":=", StringSplitOptions.TrimEntries);
		var name = args[0];
		var bracketId = name.IndexOf('<');
		if (bracketId != -1)
			name = name.Substring(0, bracketId);
		return new VerseType
		{
			Name = name
		};
	}
}

class WriteAction
{
	public List<(int id, string text)> InsertList = new();
	public List<int> DeleteList = new();
}